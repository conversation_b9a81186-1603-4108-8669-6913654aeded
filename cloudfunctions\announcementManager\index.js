// 公告管理云函数 - 增删改查
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const { action, data, announcementId } = event;

  try {
    console.log('公告管理操作:', { action, announcementId });

    switch (action) {
      case 'create':
        return await createAnnouncement(data, wxContext);
      case 'update':
        return await updateAnnouncement(announcementId, data, wxContext);
      case 'delete':
        return await deleteAnnouncement(announcementId, wxContext);
      case 'getDetail':
        return await getAnnouncementDetail(announcementId, wxContext);
      case 'updateViewCount':
        return await updateViewCount(announcementId);
      case 'batchUpdate':
        return await batchUpdateStatus(data.ids, data.status, wxContext);
      default:
        return {
          success: false,
          error: '不支持的操作类型'
        };
    }

  } catch (error) {
    console.error('公告管理操作失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 创建公告
async function createAnnouncement(data, wxContext) {
  const now = new Date();
  
  const announcement = {
    title: data.title,
    content: data.content,
    type: data.type || 'notice',
    priority: data.priority || 2,
    status: data.status || 'active',
    publishTime: data.publishTime ? new Date(data.publishTime) : now,
    effectiveTime: data.effectiveTime ? new Date(data.effectiveTime) : now,
    expireTime: data.expireTime ? new Date(data.expireTime) : new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000),
    isTop: data.isTop || false,
    viewCount: 0,
    createdBy: wxContext.OPENID || 'admin',
    createdAt: now,
    updatedAt: now
  };

  const result = await db.collection('announcements').add({
    data: announcement
  });

  return {
    success: true,
    data: {
      _id: result._id,
      ...announcement
    },
    message: '公告创建成功'
  };
}

// 更新公告
async function updateAnnouncement(announcementId, data, wxContext) {
  const now = new Date();
  
  const updateData = {
    ...data,
    updatedAt: now,
    updatedBy: wxContext.OPENID || 'admin'
  };

  // 处理时间字段
  if (data.publishTime) {
    updateData.publishTime = new Date(data.publishTime);
  }
  if (data.effectiveTime) {
    updateData.effectiveTime = new Date(data.effectiveTime);
  }
  if (data.expireTime) {
    updateData.expireTime = new Date(data.expireTime);
  }

  const result = await db.collection('announcements')
    .doc(announcementId)
    .update({
      data: updateData
    });

  return {
    success: true,
    data: result,
    message: '公告更新成功'
  };
}

// 删除公告
async function deleteAnnouncement(announcementId, wxContext) {
  const result = await db.collection('announcements')
    .doc(announcementId)
    .remove();

  return {
    success: true,
    data: result,
    message: '公告删除成功'
  };
}

// 获取公告详情
async function getAnnouncementDetail(announcementId, wxContext) {
  const result = await db.collection('announcements')
    .doc(announcementId)
    .get();

  if (result.data.length === 0) {
    return {
      success: false,
      error: '公告不存在'
    };
  }

  const announcement = result.data[0];
  const now = new Date();

  return {
    success: true,
    data: {
      ...announcement,
      isExpired: new Date(announcement.expireTime) < now,
      isEffective: new Date(announcement.effectiveTime) <= now,
      publishTimeFormatted: formatTime(announcement.publishTime),
      effectiveTimeFormatted: formatTime(announcement.effectiveTime),
      expireTimeFormatted: formatTime(announcement.expireTime)
    }
  };
}

// 更新查看次数
async function updateViewCount(announcementId) {
  const result = await db.collection('announcements')
    .doc(announcementId)
    .update({
      data: {
        viewCount: _.inc(1)
      }
    });

  return {
    success: true,
    data: result
  };
}

// 批量更新状态
async function batchUpdateStatus(ids, status, wxContext) {
  const now = new Date();
  
  const updatePromises = ids.map(id => 
    db.collection('announcements')
      .doc(id)
      .update({
        data: {
          status,
          updatedAt: now,
          updatedBy: wxContext.OPENID || 'admin'
        }
      })
  );

  await Promise.all(updatePromises);

  return {
    success: true,
    message: `批量更新${ids.length}条公告状态成功`
  };
}

// 时间格式化函数
function formatTime(date) {
  if (!date) return '';
  
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hour = String(d.getHours()).padStart(2, '0');
  const minute = String(d.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hour}:${minute}`;
}
