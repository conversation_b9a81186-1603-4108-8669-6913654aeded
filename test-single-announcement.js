// 测试单条公告显示效果的脚本
// 请在微信开发者工具的控制台中运行此代码

console.log('🧪 开始测试单条公告显示效果...');

// 获取当前页面实例
function getCurrentPageInstance() {
  const pages = getCurrentPages();
  return pages[pages.length - 1];
}

// 设置单条公告测试
function setSingleAnnouncement() {
  try {
    console.log('📝 设置单条公告测试...');
    
    const currentPage = getCurrentPageInstance();
    if (currentPage.route !== 'pages/index/index') {
      console.log('⚠️ 请在首页运行此测试');
      return;
    }
    
    // 设置单条公告数据
    const singleAnnouncement = [
      {
        _id: 'single-test-1',
        title: '🎉 单条公告测试 - 这是一个测试标题',
        content: '这是一个单条公告的测试内容，用于验证单条公告是否会出现跳跃闪烁的问题。内容稍微长一点来测试显示效果。',
        type: 'notice',
        status: 'active',
        isTop: true,
        publishTime: new Date().toISOString(),
        expireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      }
    ];
    
    currentPage.setData({
      announcements: singleAnnouncement
    });
    
    console.log('✅ 单条公告数据已设置');
    console.log('📄 公告内容:', singleAnnouncement[0]);
    
    // 检查设置结果
    setTimeout(() => {
      const announcements = currentPage.data.announcements;
      console.log('📊 设置后的公告数据:', announcements);
      console.log('📊 公告数量:', announcements ? announcements.length : 0);
    }, 500);
    
  } catch (error) {
    console.error('❌ 设置单条公告异常:', error);
  }
}

// 设置多条公告测试
function setMultipleAnnouncements() {
  try {
    console.log('📝 设置多条公告测试...');
    
    const currentPage = getCurrentPageInstance();
    if (currentPage.route !== 'pages/index/index') {
      console.log('⚠️ 请在首页运行此测试');
      return;
    }
    
    // 设置多条公告数据
    const multipleAnnouncements = [
      {
        _id: 'multi-test-1',
        title: '🎉 第一条公告',
        content: '这是第一条公告的内容，用于测试多条公告的滚动效果。',
        type: 'notice',
        status: 'active',
        isTop: true,
        publishTime: new Date().toISOString()
      },
      {
        _id: 'multi-test-2',
        title: '🔧 第二条公告',
        content: '这是第二条公告的内容，应该会自动滚动显示。',
        type: 'system',
        status: 'active',
        isTop: false,
        publishTime: new Date().toISOString()
      },
      {
        _id: 'multi-test-3',
        title: '🚨 第三条公告',
        content: '这是第三条紧急公告的内容，测试紧急公告的显示效果。',
        type: 'urgent',
        status: 'active',
        isTop: false,
        publishTime: new Date().toISOString()
      }
    ];
    
    currentPage.setData({
      announcements: multipleAnnouncements
    });
    
    console.log('✅ 多条公告数据已设置');
    console.log('📄 公告数量:', multipleAnnouncements.length);
    
    // 检查设置结果
    setTimeout(() => {
      const announcements = currentPage.data.announcements;
      console.log('📊 设置后的公告数据:', announcements);
      console.log('📊 公告数量:', announcements ? announcements.length : 0);
    }, 500);
    
  } catch (error) {
    console.error('❌ 设置多条公告异常:', error);
  }
}

// 检查公告显示元素
function checkAnnouncementElements() {
  try {
    console.log('🔍 检查公告显示元素...');
    
    const query = wx.createSelectorQuery();
    
    // 检查单条公告元素
    query.select('.single-announcement').boundingClientRect();
    
    // 检查 swiper 元素
    query.select('.announcement-swiper').boundingClientRect();
    
    // 检查公告项元素
    query.select('.announcement-item').boundingClientRect();
    
    query.exec((res) => {
      console.log('📊 公告元素查询结果:', res);
      
      if (res[0]) {
        console.log('✅ 找到单条公告元素');
        console.log('📐 单条公告位置:', res[0]);
      } else {
        console.log('ℹ️ 没有找到单条公告元素（可能是多条公告模式）');
      }
      
      if (res[1]) {
        console.log('✅ 找到 swiper 元素');
        console.log('📐 swiper 位置:', res[1]);
      } else {
        console.log('ℹ️ 没有找到 swiper 元素（可能是单条公告模式）');
      }
      
      if (res[2]) {
        console.log('✅ 找到公告项元素');
        console.log('📐 公告项位置:', res[2]);
      } else {
        console.log('❌ 没有找到公告项元素');
      }
    });
    
  } catch (error) {
    console.error('❌ 检查公告元素异常:', error);
  }
}

// 恢复原始公告数据
async function restoreOriginalAnnouncements() {
  try {
    console.log('🔄 恢复原始公告数据...');
    
    const currentPage = getCurrentPageInstance();
    if (currentPage.route !== 'pages/index/index') {
      console.log('⚠️ 请在首页运行此操作');
      return;
    }
    
    // 重新加载原始公告数据
    if (typeof currentPage.loadAnnouncements === 'function') {
      console.log('📞 重新加载原始公告数据...');
      await currentPage.loadAnnouncements();
      
      setTimeout(() => {
        const announcements = currentPage.data.announcements;
        console.log('✅ 原始公告数据已恢复');
        console.log('📊 公告数量:', announcements ? announcements.length : 0);
      }, 1000);
      
    } else {
      console.log('❌ 页面没有 loadAnnouncements 方法');
    }
    
  } catch (error) {
    console.error('❌ 恢复原始公告数据异常:', error);
  }
}

// 完整的测试流程
async function fullAnnouncementTest() {
  try {
    console.log('🧪 开始完整公告显示测试流程...');
    
    // 1. 检查当前元素
    console.log('步骤 1: 检查当前元素');
    checkAnnouncementElements();
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 2. 测试单条公告
    console.log('步骤 2: 测试单条公告显示');
    setSingleAnnouncement();
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 3. 检查单条公告元素
    console.log('步骤 3: 检查单条公告元素');
    checkAnnouncementElements();
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 4. 测试多条公告
    console.log('步骤 4: 测试多条公告显示');
    setMultipleAnnouncements();
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 5. 检查多条公告元素
    console.log('步骤 5: 检查多条公告元素');
    checkAnnouncementElements();
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 6. 恢复原始数据
    console.log('步骤 6: 恢复原始数据');
    await restoreOriginalAnnouncements();
    
    console.log('🎉 完整公告显示测试流程完成！');
    
  } catch (error) {
    console.error('❌ 完整测试流程异常:', error);
  }
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.setSingleAnnouncement = setSingleAnnouncement;
  window.setMultipleAnnouncements = setMultipleAnnouncements;
  window.checkAnnouncementElements = checkAnnouncementElements;
  window.restoreOriginalAnnouncements = restoreOriginalAnnouncements;
  window.fullAnnouncementTest = fullAnnouncementTest;
}

console.log('📋 单条公告测试脚本加载完成！');
console.log('💡 使用方法：');
console.log('  - 设置单条公告: setSingleAnnouncement()');
console.log('  - 设置多条公告: setMultipleAnnouncements()');
console.log('  - 检查显示元素: checkAnnouncementElements()');
console.log('  - 恢复原始数据: restoreOriginalAnnouncements()');
console.log('  - 完整测试流程: fullAnnouncementTest()');

console.log('🔄 自动开始测试单条公告...');
setSingleAnnouncement();
