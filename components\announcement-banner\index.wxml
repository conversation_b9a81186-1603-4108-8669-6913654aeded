<!-- 公告横幅组件 -->
<view class="announcement-banner" wx:if="{{announcements.length > 0}}">
  <!-- 公告图标和标题 -->
  <view class="announcement-header">
    <view class="announcement-icon">📢</view>
    <text class="announcement-label">公告</text>
  </view>

  <!-- 公告内容滚动区域 -->
  <view class="announcement-content">
    <!-- 根据公告数量选择显示方式 -->
    <view wx:if="{{announcements.length === 1}}" class="static-announcement-container">
      <!-- 单条公告静态显示 -->
      <view
        class="announcement-item static-item"
        bindtap="onAnnouncementTap"
        data-announcement="{{announcements[0]}}">

        <view class="announcement-text-container">
          <!-- 公告类型标识 -->
          <view class="announcement-type {{announcements[0].type}}" wx:if="{{announcements[0].type === 'urgent'}}">
            <text class="type-text">紧急</text>
          </view>

          <!-- 公告标题 -->
          <text class="announcement-title">{{announcements[0].title}}</text>

          <!-- 公告内容预览 -->
          <text class="announcement-preview" wx:if="{{announcements[0].content}}">
            {{announcements[0].content.length > 50 ? announcements[0].content.substring(0, 50) + '...' : announcements[0].content}}
          </text>
        </view>

        <!-- 查看更多箭头 -->
        <view class="announcement-arrow">
          <text class="arrow-icon">›</text>
        </view>
      </view>
    </view>

    <!-- 多条公告使用滚动显示 -->
    <view wx:elif="{{announcements.length > 1}}" class="scrolling-announcement-container">
      <swiper
        class="announcement-swiper"
        vertical="{{true}}"
        autoplay="{{true}}"
        interval="{{4000}}"
        duration="{{800}}"
        circular="{{true}}"
        display-multiple-items="{{1}}"
        easing-function="easeInOutCubic">
      
      <swiper-item 
        wx:for="{{announcements}}" 
        wx:key="_id"
        class="announcement-item"
        bindtap="onAnnouncementTap"
        data-announcement="{{item}}">
        
        <view class="announcement-text-container">
          <!-- 公告类型标识 -->
          <view class="announcement-type {{item.type}}" wx:if="{{item.type === 'urgent'}}">
            <text class="type-text">紧急</text>
          </view>
          
          <!-- 公告标题 -->
          <text class="announcement-title">{{item.title}}</text>
          
          <!-- 公告内容预览 -->
          <text class="announcement-preview" wx:if="{{item.content}}">
            {{item.content.length > 50 ? item.content.substring(0, 50) + '...' : item.content}}
          </text>
        </view>

        <!-- 查看更多箭头 -->
        <view class="announcement-arrow">
          <text class="arrow-icon">›</text>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 关闭按钮 -->
  <view class="announcement-close" bindtap="onCloseBanner" wx:if="{{showCloseButton}}">
    <text class="close-icon">×</text>
  </view>
</view>

<!-- 公告详情弹窗 -->
<view class="announcement-modal {{showModal ? 'show' : ''}}" wx:if="{{showModal}}">
  <view class="modal-mask" bindtap="onCloseModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <view class="modal-title-container">
        <view class="modal-type {{selectedAnnouncement.type}}" wx:if="{{selectedAnnouncement.type === 'urgent'}}">
          <text class="type-text">紧急</text>
        </view>
        <text class="modal-title">{{selectedAnnouncement.title}}</text>
      </view>
      <view class="modal-close" bindtap="onCloseModal">
        <text class="close-icon">×</text>
      </view>
    </view>
    
    <scroll-view class="modal-body" scroll-y="{{true}}">
      <text class="modal-content-text">{{selectedAnnouncement.content}}</text>
      
      <view class="modal-meta">
        <text class="meta-item">发布时间：{{selectedAnnouncement.publishTimeFormatted}}</text>
        <text class="meta-item" wx:if="{{selectedAnnouncement.expireTimeFormatted}}">
          有效期至：{{selectedAnnouncement.expireTimeFormatted}}
        </text>
      </view>
    </scroll-view>
    
    <view class="modal-footer">
      <button class="modal-btn confirm" bindtap="onCloseModal">知道了</button>
    </view>
  </view>
</view>
