// 验证静态公告修复效果的脚本
// 请在微信开发者工具的控制台中运行此代码

console.log('🔍 验证静态公告修复效果...');

// 获取当前页面实例
function getCurrentPageInstance() {
  const pages = getCurrentPages();
  return pages[pages.length - 1];
}

// 强制设置单条公告并验证
function forceSetSingleAnnouncement() {
  try {
    console.log('🔧 强制设置单条公告...');
    
    const currentPage = getCurrentPageInstance();
    if (currentPage.route !== 'pages/index/index') {
      console.log('⚠️ 请在首页运行此测试');
      return;
    }
    
    // 清除所有缓存
    try {
      wx.removeStorageSync('announcement_list_cache');
      console.log('✅ 已清除公告缓存');
    } catch (e) {
      console.log('ℹ️ 缓存清除失败或不存在');
    }
    
    // 设置单条静态公告
    const staticAnnouncement = [
      {
        _id: 'static-test-' + Date.now(),
        title: '🎯 静态公告测试 - 不应该有任何跳跃动画',
        content: '这是一个静态公告测试，用于验证单条公告是否完全静态显示，不会出现从上到下的跳跃闪烁效果。如果您看到这条公告在跳跃，说明修复还没有生效。',
        type: 'notice',
        status: 'active',
        isTop: true,
        publishTime: new Date().toISOString(),
        expireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      }
    ];
    
    // 强制设置数据
    currentPage.setData({
      announcements: staticAnnouncement
    });
    
    console.log('✅ 单条静态公告已设置');
    console.log('📄 公告内容:', staticAnnouncement[0].title);
    
    // 验证设置结果
    setTimeout(() => {
      const announcements = currentPage.data.announcements;
      console.log('📊 验证结果:');
      console.log('  - 公告数量:', announcements ? announcements.length : 0);
      console.log('  - 应该显示静态容器:', announcements && announcements.length === 1);
      
      // 检查DOM元素
      checkStaticElements();
      
    }, 1000);
    
  } catch (error) {
    console.error('❌ 设置单条公告异常:', error);
  }
}

// 检查静态元素是否正确渲染
function checkStaticElements() {
  try {
    console.log('🔍 检查静态元素渲染...');
    
    const query = wx.createSelectorQuery();
    
    // 检查静态容器
    query.select('.static-announcement-container').boundingClientRect();
    
    // 检查静态公告项
    query.select('.announcement-item.static-item').boundingClientRect();
    
    // 检查是否存在swiper（不应该存在）
    query.select('.announcement-swiper').boundingClientRect();
    
    // 检查滚动容器（不应该存在）
    query.select('.scrolling-announcement-container').boundingClientRect();
    
    query.exec((res) => {
      console.log('📊 元素检查结果:');
      
      if (res[0]) {
        console.log('✅ 找到静态容器 (.static-announcement-container)');
        console.log('📐 静态容器位置:', res[0]);
      } else {
        console.log('❌ 没有找到静态容器');
      }
      
      if (res[1]) {
        console.log('✅ 找到静态公告项 (.announcement-item.static-item)');
        console.log('📐 静态公告项位置:', res[1]);
      } else {
        console.log('❌ 没有找到静态公告项');
      }
      
      if (res[2]) {
        console.log('⚠️ 意外找到swiper元素（单条公告时不应该存在）');
        console.log('📐 swiper位置:', res[2]);
      } else {
        console.log('✅ 正确：没有找到swiper元素');
      }
      
      if (res[3]) {
        console.log('⚠️ 意外找到滚动容器（单条公告时不应该存在）');
        console.log('📐 滚动容器位置:', res[3]);
      } else {
        console.log('✅ 正确：没有找到滚动容器');
      }
      
      // 总结
      const isCorrect = res[0] && res[1] && !res[2] && !res[3];
      if (isCorrect) {
        console.log('🎉 静态公告渲染正确！应该不会有跳跃动画了');
      } else {
        console.log('⚠️ 静态公告渲染可能有问题，请检查');
      }
    });
    
  } catch (error) {
    console.error('❌ 检查静态元素异常:', error);
  }
}

// 测试多条公告切换
function testMultipleAnnouncementSwitch() {
  try {
    console.log('🔄 测试多条公告切换...');
    
    const currentPage = getCurrentPageInstance();
    if (currentPage.route !== 'pages/index/index') {
      console.log('⚠️ 请在首页运行此测试');
      return;
    }
    
    // 设置多条公告
    const multipleAnnouncements = [
      {
        _id: 'multi-1-' + Date.now(),
        title: '📢 第一条滚动公告',
        content: '这是第一条公告，应该会自动滚动。',
        type: 'notice',
        status: 'active',
        isTop: true,
        publishTime: new Date().toISOString()
      },
      {
        _id: 'multi-2-' + Date.now(),
        title: '🔧 第二条滚动公告',
        content: '这是第二条公告，应该会自动滚动。',
        type: 'system',
        status: 'active',
        isTop: false,
        publishTime: new Date().toISOString()
      }
    ];
    
    currentPage.setData({
      announcements: multipleAnnouncements
    });
    
    console.log('✅ 多条公告已设置，应该看到滚动效果');
    
    // 检查多条公告元素
    setTimeout(() => {
      const query = wx.createSelectorQuery();
      
      query.select('.scrolling-announcement-container').boundingClientRect();
      query.select('.announcement-swiper').boundingClientRect();
      query.select('.static-announcement-container').boundingClientRect();
      
      query.exec((res) => {
        console.log('📊 多条公告元素检查:');
        
        if (res[0]) {
          console.log('✅ 找到滚动容器');
        } else {
          console.log('❌ 没有找到滚动容器');
        }
        
        if (res[1]) {
          console.log('✅ 找到swiper元素');
        } else {
          console.log('❌ 没有找到swiper元素');
        }
        
        if (res[2]) {
          console.log('⚠️ 意外找到静态容器（多条公告时不应该存在）');
        } else {
          console.log('✅ 正确：没有找到静态容器');
        }
      });
    }, 1000);
    
  } catch (error) {
    console.error('❌ 测试多条公告切换异常:', error);
  }
}

// 完整验证流程
async function fullVerificationProcess() {
  try {
    console.log('🧪 开始完整验证流程...');
    
    // 1. 强制设置单条公告
    console.log('步骤 1: 设置单条静态公告');
    forceSetSingleAnnouncement();
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 2. 测试多条公告
    console.log('步骤 2: 测试多条公告滚动');
    testMultipleAnnouncementSwitch();
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 3. 再次回到单条公告
    console.log('步骤 3: 再次测试单条静态公告');
    forceSetSingleAnnouncement();
    
    console.log('🎉 完整验证流程完成！');
    console.log('💡 观察要点：');
    console.log('  - 单条公告应该完全静态，无任何动画');
    console.log('  - 多条公告应该正常滚动');
    console.log('  - 切换时应该无缝过渡');
    
  } catch (error) {
    console.error('❌ 完整验证流程异常:', error);
  }
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.forceSetSingleAnnouncement = forceSetSingleAnnouncement;
  window.checkStaticElements = checkStaticElements;
  window.testMultipleAnnouncementSwitch = testMultipleAnnouncementSwitch;
  window.fullVerificationProcess = fullVerificationProcess;
}

console.log('📋 静态公告验证脚本加载完成！');
console.log('💡 使用方法：');
console.log('  - 强制设置单条公告: forceSetSingleAnnouncement()');
console.log('  - 检查静态元素: checkStaticElements()');
console.log('  - 测试多条公告: testMultipleAnnouncementSwitch()');
console.log('  - 完整验证流程: fullVerificationProcess()');

console.log('🔄 自动开始验证...');
forceSetSingleAnnouncement();
