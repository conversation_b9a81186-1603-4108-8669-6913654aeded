/* 公告横幅组件样式 */
.announcement-banner {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 152, 0, 0.1));
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  margin: 0 24rpx 20rpx;
  border: 1rpx solid rgba(255, 193, 7, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(255, 193, 7, 0.2);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  min-height: 80rpx;
  animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.announcement-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, #ffc107, transparent);
  animation: scanLine 3s ease-in-out infinite;
}

@keyframes scanLine {
  0%, 100% { opacity: 0; transform: translateX(-100%); }
  50% { opacity: 1; transform: translateX(100%); }
}

.announcement-header {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  flex-shrink: 0;
}

.announcement-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4rpx);
  }
  60% {
    transform: translateY(-2rpx);
  }
}

.announcement-label {
  font-size: 24rpx;
  font-weight: 600;
  color: #ff8f00;
  text-shadow: 0 2rpx 8rpx rgba(255, 143, 0, 0.3);
}

.announcement-content {
  flex: 1;
  height: 80rpx;
  overflow: hidden;
}

/* 静态公告容器（单条公告） */
.static-announcement-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
}

/* 滚动公告容器（多条公告） */
.scrolling-announcement-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 静态公告项样式 */
.announcement-item.static-item {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
  box-sizing: border-box;
  /* 确保完全静态，无任何动画 */
  position: static;
  transform: none;
  transition: none;
}

.announcement-swiper {
  width: 100%;
  height: 100%;
}

.announcement-item {
  display: flex;
  align-items: center;
  padding: 0 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.announcement-item:active {
  background: rgba(255, 193, 7, 0.1);
}

.announcement-text-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 60rpx;
}

.announcement-type {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin-bottom: 4rpx;
  align-self: flex-start;
}

.announcement-type.urgent {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);
}

.announcement-type .type-text {
  font-size: 18rpx;
  color: white;
  font-weight: 600;
  line-height: 1;
}

.announcement-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.3;
  margin-bottom: 2rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.announcement-preview {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.announcement-arrow {
  padding: 0 16rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.arrow-icon {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.6);
  font-weight: bold;
  transition: all 0.3s ease;
}

.announcement-item:active .arrow-icon {
  color: #ffc107;
  transform: translateX(4rpx);
}

.announcement-close {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.announcement-close:active {
  background: rgba(0, 0, 0, 0.5);
  transform: scale(0.9);
}

.close-icon {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: bold;
  line-height: 1;
}

/* 公告详情弹窗样式 */
.announcement-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.announcement-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10rpx);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: linear-gradient(135deg, rgba(30, 45, 61, 0.95), rgba(21, 32, 43, 0.95));
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 193, 7, 0.3);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.modal-title-container {
  flex: 1;
  margin-right: 16rpx;
}

.modal-type {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
}

.modal-type.urgent {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.4;
  word-break: break-word;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.modal-close:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.9);
}

.modal-body {
  flex: 1;
  padding: 24rpx;
  max-height: 60vh;
}

.modal-content-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  word-break: break-word;
  white-space: pre-wrap;
}

.modal-meta {
  margin-top: 24rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
}

.meta-item {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8rpx;
}

.modal-footer {
  padding: 20rpx 24rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
}

.modal-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #ffc107, #ff8f00);
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(255, 193, 7, 0.4);
  transition: all 0.3s ease;
}

.modal-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 193, 7, 0.6);
}
