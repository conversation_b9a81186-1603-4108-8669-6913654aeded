// 公告横幅组件
Component({
  properties: {
    // 公告列表
    announcements: {
      type: Array,
      value: []
    },
    // 是否显示关闭按钮
    showCloseButton: {
      type: Boolean,
      value: false
    }
  },

  data: {
    showModal: false,
    selectedAnnouncement: {}
  },

  methods: {
    // 点击公告项
    onAnnouncementTap(e) {
      const announcement = e.currentTarget.dataset.announcement;
      if (!announcement) return;

      console.log('点击公告:', announcement.title);

      // 更新查看次数
      this.updateViewCount(announcement._id);

      // 跳转到公告详情页面
      wx.navigateTo({
        url: `/pages/announcement/detail/detail?id=${announcement._id}`
      });

      // 触发自定义事件
      this.triggerEvent('announcementTap', {
        announcement: announcement
      });
    },

    // 关闭横幅
    onCloseBanner() {
      console.log('关闭公告横幅');
      
      // 触发自定义事件
      this.triggerEvent('closeBanner');
    },

    // 关闭详情弹窗
    onCloseModal() {
      this.setData({
        showModal: false,
        selectedAnnouncement: {}
      });
    },

    // 更新查看次数
    async updateViewCount(announcementId) {
      try {
        await wx.cloud.callFunction({
          name: 'announcementManager',
          data: {
            action: 'updateViewCount',
            announcementId: announcementId
          }
        });
      } catch (error) {
        console.error('更新公告查看次数失败:', error);
      }
    }
  }
});
